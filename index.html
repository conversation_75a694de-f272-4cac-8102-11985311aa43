<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$LUCK 🍀 - All You Need Is LUCK</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap" rel="stylesheet">
</head>
<body>
    <div class="matrix-bg"></div>
    <nav class="navbar rainbow-border">
        <div class="container">
            <div class="logo glitch" data-text="$LUCK 🍀">$LUCK 🍀</div>
            <ul class="nav-links">
                <li><a href="#home" class="sparkle">Home</a></li>
                <li><a href="#about" class="sparkle">About</a></li>
                <li><a href="#tokenomics" class="sparkle">Tokenomics</a></li>
                <li><a href="#roadmap" class="sparkle">Roadmap</a></li>
            </ul>
        </div>
    </nav>

    <section id="home" class="hero">
        <div class="lucky-particles"></div>
        <div class="floating-memes">
            <div class="meme doge"></div>
            <div class="meme pepe"></div>
            <div class="meme stonks"></div>
        </div>
        <div class="container">
            <div class="title-container">
                <h1 class="glitch" data-text="All You Need Is $LUCK"><span>All You Need Is <span class="luck-text">$LUCK</span></span> <span class="coin">🍀</span></h1>
            </div>
            <p class="subtitle sparkle">Your Lucky Charm in the Solana Ecosystem</p>
            <div class="contract-address hover-card">
                <code id="contract-address">Ak1StSUAardZ157jSQu4hMkkoPFiUowttuowUeompump</code>
                <button id="copy-address" class="copy-btn sparkle">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
            <div class="cta-buttons">
                <a href="https://raydium.io/swap/?inputMint=sol&outputMint=Ak1StSUAardZ157jSQu4hMkkoPFiUowttuowUeompump" target="_blank" class="btn primary rainbow-border">Buy $LUCK 🍀</a>
                <a href="https://dexscreener.com/solana/62xf5by99mkcryrhbbxf7kgmndcbdyqzu9yvmhcsdv4r" target="_blank" class="btn secondary">View Chart</a>
            </div>
        </div>
    </section>

    <section id="about" class="about">
        <div class="container">
            <h2 class="glitch" data-text="About $LUCK 🍀">About $LUCK 🍀</h2>
            <p class="sparkle">$LUCK is a community-powered meme token on Solana inspired by four-leaf clovers, good fortune, and relentless conviction. Built by a grassroots team of holders and raiders, $LUCK aims to bring positive energy and long-term value to the meme coin space. No dev wallet. No VC backing. Just real people pushing for real upside. 🍀</p>
            <div class="features">
                <div class="feature hover-card rainbow-border">
                    <i class="fas fa-clover"></i>
                    <h3>Lucky Token</h3>
                    <p>Your gateway to good fortune in crypto</p>
                </div>
                <div class="feature hover-card rainbow-border">
                    <i class="fas fa-bolt"></i>
                    <h3>Solana Power</h3>
                    <p>Lightning-fast transactions on Solana</p>
                </div>
                <div class="feature hover-card rainbow-border">
                    <i class="fas fa-users"></i>
                    <h3>Lucky Community</h3>
                    <p>Join our growing family of lucky holders</p>
                </div>
            </div>
        </div>
    </section>

    <section id="tokenomics" class="tokenomics">
        <div class="container">
            <h2 class="glitch" data-text="Tokenomics">Tokenomics</h2>
            <div class="tokenomics-grid">
                <div class="token-info hover-card pixel-border">
                    <h3 class="sparkle">Total Supply</h3>
                    <p>TODO $LUCK</p>
                </div>
                <div class="token-info hover-card pixel-border">
                    <h3 class="sparkle">Initial Burn</h3>
                    <p>TODO 🔥</p>
                </div>
                <div class="token-info hover-card pixel-border">
                    <h3 class="sparkle">Liquidity Pool</h3>
                    <p>TODO 💧</p>
                </div>
            </div>
        </div>
    </section>

    <section id="roadmap" class="roadmap">
        <div class="container">
            <h2 class="glitch" data-text="Lucky Roadmap">Lucky Roadmap</h2>
            <div class="timeline">
                <div class="phase hover-card rainbow-border">
                    <h3 class="sparkle">Phase 1: Genesis 🌱</h3>
                    <ul>
                        <li>Website Launch</li>
                        <li>Community Building</li>
                        <li>Token Launch on Solana</li>
                    </ul>
                </div>
                <div class="phase hover-card rainbow-border">
                    <h3 class="sparkle">Phase 2: Growth 🌿</h3>
                    <ul>
                        <li>TODO</li>
                        <li>TODO</li>
                        <li>TODO</li>
                    </ul>
                </div>
                <div class="phase hover-card rainbow-border">
                    <h3 class="sparkle">Phase 3: Fortune 🍀</h3>
                    <ul>
                        <li>TODO</li>
                        <li>TODO</li>
                        <li>TODO</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <footer class="rainbow-border">
        <div class="container">
            <div class="social-links">
                <a href="https://x.com/Luck_Official_X" target="_blank" class="hover-card"><i class="fa-brands fa-x-twitter"></i></a>
                <a href="https://t.me/LUCK_TG_Portal" target="_blank" class="hover-card"><i class="fab fa-telegram"></i></a>
            </div>
            <p class="sparkle">May the $LUCK be with you! 🍀</p>
            <p class="glitch" data-text="© 2025 $LUCK. All rights reserved.">&copy; 2025 $LUCK. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html> 