// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add animation on scroll
const observerOptions = {
    threshold: 0.1
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe all sections
document.querySelectorAll('section').forEach(section => {
    section.style.opacity = '0';
    section.style.transform = 'translateY(20px)';
    section.style.transition = 'all 0.6s ease-out';
    observer.observe(section);
});

// Enhanced copy contract address functionality
function copyToClipboard(text) {
    // Create a temporary textarea element
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    document.body.appendChild(textarea);
    
    try {
        // Select and copy the text
        textarea.select();
        document.execCommand('copy');
        return true;
    } catch (err) {
        console.error('Failed to copy:', err);
        return false;
    } finally {
        // Clean up
        document.body.removeChild(textarea);
    }
}

// Modern clipboard API with fallback
async function copyAddress(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // Use modern Clipboard API
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // Fallback for older browsers
            return copyToClipboard(text);
        }
    } catch (err) {
        console.error('Copy failed:', err);
        return false;
    }
}

// Update copy button functionality
document.getElementById('copy-address').addEventListener('click', async () => {
    const contractAddress = document.getElementById('contract-address').textContent;
    const button = document.getElementById('copy-address');
    const icon = button.querySelector('i');
    
    const success = await copyAddress(contractAddress);
    
    if (success) {
        // Visual feedback
        button.classList.add('copied');
        icon.classList.remove('fa-copy');
        icon.classList.add('fa-check');
        
        // Add a subtle glow effect to the address
        const addressElement = document.getElementById('contract-address');
        addressElement.style.color = 'var(--lucky-green)';
        addressElement.style.textShadow = '0 0 15px var(--lucky-green)';
        
        // Reset after 2 seconds
        setTimeout(() => {
            button.classList.remove('copied');
            icon.classList.remove('fa-check');
            icon.classList.add('fa-copy');
            addressElement.style.color = '';
            addressElement.style.textShadow = '';
        }, 2000);
    } else {
        // Error feedback
        button.style.background = 'rgba(255, 0, 0, 0.2)';
        icon.classList.remove('fa-copy');
        icon.classList.add('fa-times');
        
        // Reset after 2 seconds
        setTimeout(() => {
            button.style.background = '';
            icon.classList.remove('fa-times');
            icon.classList.add('fa-copy');
        }, 2000);
    }
});

// Add click-to-copy functionality to the address text itself
document.getElementById('contract-address').addEventListener('click', async () => {
    const success = await copyAddress(document.getElementById('contract-address').textContent);
    if (success) {
        const element = document.getElementById('contract-address');
        const originalColor = element.style.color;
        const originalShadow = element.style.textShadow;
        
        element.style.color = 'var(--lucky-green)';
        element.style.textShadow = '0 0 15px var(--lucky-green)';
        
        setTimeout(() => {
            element.style.color = originalColor;
            element.style.textShadow = originalShadow;
        }, 1000);
    }
});

// Create floating clovers background
function createClover(container) {
    const clover = document.createElement('div');
    clover.className = 'clover';
    
    // Random size class
    const sizeRandom = Math.random();
    if (sizeRandom > 0.8) {
        clover.classList.add('large');
        clover.style.setProperty('--clover-opacity', '0.18');
    } else if (sizeRandom < 0.3) {
        clover.classList.add('small');
        clover.style.setProperty('--clover-opacity', '0.25');
    } else {
        clover.style.setProperty('--clover-opacity', '0.22');
    }
    
    clover.innerHTML = '🍀';
    
    // Random horizontal starting position
    const startX = Math.random() * (container.offsetWidth - 40); // Account for clover size
    const driftX = (Math.random() - 0.5) * 200; // Random drift between -100px and 100px
    
    // Animation timing
    const duration = 7 + Math.random() * 6; // 7-13 seconds
    const delay = Math.random() * -5; // Shorter initial delay
    
    // Set position and animation properties
    clover.style.setProperty('--start-x', `${startX}px`);
    clover.style.setProperty('--drift-x', `${driftX}px`);
    clover.style.setProperty('--duration', `${duration}s`);
    clover.style.setProperty('--delay', `${delay}s`);
    
    // Add to container
    container.appendChild(clover);
    
    // Remove and recreate clover when animation ends
    clover.addEventListener('animationend', () => {
        clover.remove();
        // Only create a new one if we're under the limit
        if (container.childNodes.length < 12) {
            createClover(container);
        }
    });
}

function createFloatingClovers() {
    // Remove any existing floating-clovers container
    const existingContainer = document.querySelector('.floating-clovers');
    if (existingContainer) {
        existingContainer.remove();
    }

    const hero = document.querySelector('.hero');
    if (!hero) return;

    const container = document.createElement('div');
    container.className = 'floating-clovers';
    hero.appendChild(container);

    // Create initial set of clovers with staggered timing
    const numberOfClovers = 12;
    for (let i = 0; i < numberOfClovers; i++) {
        setTimeout(() => {
            if (container.childNodes.length < numberOfClovers) {
                createClover(container);
            }
        }, i * 400); // Stagger creation every 400ms
    }

    // Periodically check and add new clovers
    const interval = setInterval(() => {
        if (!document.body.contains(hero)) {
            clearInterval(interval);
            return;
        }
        if (container.childNodes.length < numberOfClovers) {
            createClover(container);
        }
    }, 1000);
}

// Update matrix rain effect
function createMatrixRain() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const matrixBg = document.querySelector('.matrix-bg');
    
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.opacity = '0.15';
    
    matrixBg.appendChild(canvas);
    
    function resizeCanvas() {
        canvas.width = matrixBg.offsetWidth;
        canvas.height = matrixBg.offsetHeight;
    }
    
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
    
    const chars = '01$LUCK🍀';
    const fontSize = 16;
    const columns = Math.floor(canvas.width / fontSize);
    const drops = Array(Math.floor(columns)).fill(1);
    
    function draw() {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.fillStyle = 'var(--lucky-green)';
        ctx.font = `${fontSize}px monospace`; // Changed to monospace for better performance
        
        // Remove shadow effects for better performance
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        
        for (let i = 0; i < drops.length; i++) {
            const text = chars[Math.floor(Math.random() * chars.length)];
            ctx.fillText(text, i * fontSize, drops[i] * fontSize);
            
            if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                drops[i] = 0;
            }
            drops[i]++;
        }
        
        // Use requestAnimationFrame instead of setInterval for better performance
        requestAnimationFrame(draw);
    }
    
    requestAnimationFrame(draw);
}

// Update particle effect
function createLuckyParticles() {
    const container = document.querySelector('.lucky-particles');
    const colors = [
        'var(--rainbow-1)',
        'var(--rainbow-2)',
        'var(--rainbow-3)',
        'var(--rainbow-4)',
        'var(--fortune-gold)',
        'var(--neon-blue)'
    ];

    // Reduce number of particles
    for (let i = 0; i < 15; i++) { // Reduced from 30 to 15
        const particle = document.createElement('div');
        particle.className = 'lucky-particle';
        
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        
        const color = colors[Math.floor(Math.random() * colors.length)];
        particle.style.background = color;
        // Reduce shadow intensity
        particle.style.boxShadow = `0 0 5px ${color}`;
        
        const size = 3 + Math.random() * 6;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        
        particle.style.animationDelay = `${Math.random() * 4}s`;
        // Increase animation duration for smoother movement
        particle.style.animationDuration = `${6 + Math.random() * 4}s`;
        
        particle.style.setProperty('--x', `${-100 + Math.random() * 200}px`);
        particle.style.setProperty('--y', `${-100 + Math.random() * 200}px`);
        
        container.appendChild(particle);
    }
}

// Update floating memes with better positioning
function createFloatingMemes() {
    const container = document.querySelector('.floating-memes');
    const memes = container.querySelectorAll('.meme');
    
    memes.forEach(meme => {
        // Random position within viewport
        const randomX = 20 + Math.random() * 60; // Keep within middle 60% of width
        const randomY = 20 + Math.random() * 60; // Keep within middle 60% of height
        meme.style.left = `${randomX}%`;
        meme.style.top = `${randomY}%`;
        
        // Random animation delay
        meme.style.animationDelay = `${Math.random() * 2}s`;
        
        // Random rotation
        meme.style.transform = `rotate(${Math.random() * 360}deg)`;
        
        // Add glow effect
        meme.style.filter = 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.3))';
    });
}

// Add glitch effect to text
function addGlitchEffect() {
    const glitchElements = document.querySelectorAll('.glitch');
    
    glitchElements.forEach(element => {
        if (!element.hasAttribute('data-text')) {
            element.setAttribute('data-text', element.textContent);
        }
    });
}

// Add sparkle effect
function addSparkleEffect() {
    const sparkleElements = document.querySelectorAll('.sparkle');
    
    sparkleElements.forEach(element => {
        element.addEventListener('mouseover', () => {
            const sparkle = document.createElement('span');
            sparkle.textContent = '✨';
            sparkle.style.position = 'absolute';
            sparkle.style.left = `${Math.random() * 100}%`;
            sparkle.style.top = `${Math.random() * 100}%`;
            sparkle.style.animation = 'sparkle-float 1s ease-out forwards';
            element.appendChild(sparkle);
            
            setTimeout(() => sparkle.remove(), 1000);
        });
    });
}

// Add coin flip effect
function addCoinFlipEffect() {
    const coins = document.querySelectorAll('.coin');
    
    coins.forEach(coin => {
        coin.addEventListener('click', () => {
            coin.style.animation = 'none';
            setTimeout(() => {
                coin.style.animation = 'coin-flip 2s ease-in-out';
            }, 10);
        });
    });
}

// Add mobile optimization function
function optimizeForMobile() {
    if (window.innerWidth < 768) {
        // Reduce particles even further on mobile
        const particles = document.querySelectorAll('.lucky-particle');
        particles.forEach((particle, index) => {
            if (index > 7) { // Keep only 8 particles on mobile
                particle.remove();
            }
        });
        
        // Disable matrix rain on mobile
        const matrixBg = document.querySelector('.matrix-bg');
        if (matrixBg) {
            matrixBg.style.display = 'none';
        }

        // Reduce number of floating clovers on mobile
        const clovers = document.querySelectorAll('.clover');
        clovers.forEach((clover, index) => {
            if (index > 5) { // Keep only 6 clovers on mobile
                clover.remove();
            }
        });
    }
}

// Initialize all effects
document.addEventListener('DOMContentLoaded', () => {
    createLuckyParticles();
    //createFloatingMemes();
    //addGlitchEffect();
    //addSparkleEffect();
    //addCoinFlipEffect();
    //createMatrixRain();
    createFloatingClovers();
    //optimizeForMobile(); // Add mobile optimization
    
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
});
